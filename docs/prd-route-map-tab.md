# Product Requirements Document: Route Map Tab for RFQ View

## Overview

This PRD outlines the implementation of a new "Route Map" tab in the RFQ view page that displays an interactive Google Maps route visualization showing the path between pickup and delivery locations, along with pre-calculated distance information. This feature will enhance the RFQ management experience by providing visual route context for logistics planning while optimizing performance through distance caching.

## User Stories

### Primary User Story
**As a logistics coordinator**, I want to view the route between pickup and delivery locations on an interactive map so that I can better understand the transportation requirements and make informed decisions about provider selection.

### Secondary User Stories
- **As a logistics coordinator**, I want to see the calculated distance for the route so that I can validate pricing and time estimates
- **As a logistics coordinator**, I want the distance to load instantly without waiting for API calls so that I can quickly assess route feasibility
- **As a logistics coordinator**, I want the map to handle cases where coordinates are not available gracefully so that the system remains functional
- **As a logistics coordinator**, I want the route map to be responsive and consistent with the existing UI so that it feels integrated with the platform

## Technical Requirements

### Core Dependencies
- [ ] **@vis.gl/react-google-maps**: React components for Google Maps integration
- [ ] **Google Distance Matrix API**: For optimized distance calculation during RFQ creation/updates
- [ ] **Google Routes API**: For real-time route visualization and rendering
- [ ] **Existing Google Maps API Key**: Already configured as `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY`

### Architecture Compliance
- [ ] **Service-Layer Pattern**: Implement route calculation service before actions/components
- [ ] **Next.js 15 App Router**: Use Server Components where possible, Client Components only when necessary
- [ ] **Steelflow UI Patterns**: Consistent with existing tab structure and loading states
- [ ] **Error Handling**: Comprehensive error boundaries and fallback states

### Data Integration
- [ ] **RFQ Data Structure**: Extract coordinates from existing RFQ pickup/delivery locations
- [ ] **Distance Caching**: Store pre-calculated distances in RFQ database table
- [ ] **Geocoding Fallback**: Use existing Google Maps service for coordinate resolution when needed
- [ ] **Location Display**: Integrate with existing `LocationDisplay` components

### Performance Optimization Strategy
- [ ] **Coordinate Caching**: Store resolved lat/lng coordinates to eliminate repeated geocoding
- [ ] **Distance Matrix API**: Use cached coordinates for efficient distance calculation
- [ ] **Database Storage**: Cache both coordinates and distances to minimize API calls
- [ ] **Selective Recalculation**: Trigger updates only when addresses change (detected via hashing)
- [ ] **Fast Loading**: Display cached coordinates and distances instantly without API delays
- [ ] **Consolidated Component Architecture**: Single route map component reduces file complexity and improves maintainability

### Coordinate Storage Strategy
This section addresses the relationship between address fields and coordinate caching:

#### **1. Database Schema Design**
- **Coordinate Columns**: Add `origin_lat`, `origin_lng`, `destination_lat`, `destination_lng` to RFQ table
- **Address-Coordinate Relationship**: Coordinates are derived from and linked to existing address fields:
  - `origin_lat/lng` ← derived from `origin_city`, `origin_address`, `origin_postal_code`
  - `destination_lat/lng` ← derived from `destination_city`, `destination_address`, `destination_postal_code`
- **Change Detection**: `address_hash` column stores hash of all address fields to detect changes

#### **2. Flexible Coordinate Resolution Workflow**
- **Minimum Requirements**: At least city + postal code OR city + country for meaningful geocoding
- **Address Building Strategy**: Flexible fallback approach for partial address data:
  1. **Full Address**: Use complete street address + city + postal code when available
  2. **City + Postal**: Use city + postal code when street address is missing
  3. **City + Country**: Use city + country as fallback when postal code is unavailable
- **RFQ Creation**: Geocode addresses using flexible strategy → store coordinates → calculate distance
- **RFQ Updates**: Compare address hash → re-geocode if changed → recalculate distance if coordinates changed
- **Fallback Strategy**: If geocoding fails, store NULL coordinates and use address-based fallbacks

#### **3. Cache Invalidation Strategy**
- **Address Change Detection**: Hash comparison of `(origin_city + origin_address + origin_postal_code + destination_city + destination_address + destination_postal_code)`
- **Coordinate Refresh**: Re-geocode only when address hash changes
- **Distance Refresh**: Recalculate only when coordinates change
- **Atomic Updates**: Update coordinates, distance, and timestamps together to maintain consistency

#### **4. Data Consistency Rules**
- **Source of Truth**: Address fields remain the authoritative source
- **Coordinate Derivation**: Coordinates are always derived from addresses using flexible building strategy
- **Minimum Data Requirements**: City + postal code are mandatory for coordinate resolution
- **Timestamp Tracking**: `coordinates_resolved_at` and `route_distance_calculated_at` track cache freshness
- **Error Handling**: Failed geocoding preserves existing coordinates until successful resolution

## Implementation Tasks

### Phase 1: Database Schema & Coordinate/Distance Caching
- [x] **1.1** Create database migration for RFQ coordinate and distance storage
  - [x] Add coordinate columns to `rfqs` table:
    - [x] `origin_lat` (DECIMAL(10,8)) - Origin latitude
    - [x] `origin_lng` (DECIMAL(11,8)) - Origin longitude
    - [x] `destination_lat` (DECIMAL(10,8)) - Destination latitude
    - [x] `destination_lng` (DECIMAL(11,8)) - Destination longitude
  - [x] Add distance and metadata columns:
    - [x] `route_distance_km` (DECIMAL(8,2)) - Cached route distance
    - [x] `coordinates_resolved_at` (TIMESTAMP) - When coordinates were geocoded
    - [x] `route_distance_calculated_at` (TIMESTAMP) - When distance was calculated
    - [x] `address_hash` (VARCHAR(64)) - Hash of address fields for change detection
  - [x] Add database indexes for performance:
    - [x] Index on coordinate columns for spatial queries
    - [x] Index on timestamp columns for cache validation
  - [x] Update RFQ schema types and validation (via supazod)

- [x] **1.2** Create `coordinate-resolution.service.ts` in `src/lib/services/google-maps/`
  - [x] Implement geocoding service for address-to-coordinate conversion
  - [x] Handle batch coordinate resolution for multiple addresses
  - [x] Add address validation and normalization
  - [x] Include comprehensive error handling and retry logic
  - [x] Implement address change detection using hashing

- [x] **1.3** Create `distance-calculation.service.ts` in `src/lib/services/google-maps/`
  - [x] Implement Distance Matrix API integration using cached coordinates
  - [x] Handle batch distance calculations
  - [x] Add coordinate validation before distance calculation
  - [x] Include comprehensive error handling and retry logic

- [x] **1.4** Create coordinate and distance types in `src/lib/services/google-maps/types.ts`
  - [x] `CoordinateResolutionResult` interface
  - [x] `CachedCoordinates` interface
  - [x] `DistanceCalculationResult` interface
  - [x] `RouteCoordinates` interface
  - [x] `DistanceMatrixResponse` types
  - [x] `AddressChangeDetection` interface

### Phase 2: RFQ Lifecycle Integration
- [x] **2.1** Update RFQ creation workflow in `rfq.service.ts`
  - [x] **Step 1**: Resolve coordinates from origin/destination addresses using geocoding
  - [x] **Step 2**: Calculate distance using resolved coordinates and Distance Matrix API
  - [x] **Step 3**: Store coordinates, distance, and metadata in database
  - [x] **Step 4**: Generate address hash for change detection
  - [x] Handle coordinate resolution and distance calculation failures gracefully

- [x] **2.2** Update RFQ modification workflow
  - [x] **Address Change Detection**: Compare current address hash with stored hash
  - [x] **Conditional Coordinate Resolution**: Re-geocode only when addresses change
  - [x] **Conditional Distance Recalculation**: Recalculate only when coordinates change
  - [x] **Atomic Updates**: Update coordinates, distance, and timestamps together
  - [x] **Fallback Strategy**: Maintain existing data if new calculations fail

- [x] **2.3** Create coordinate and distance actions in `src/lib/actions/`
  - [x] `coordinate.actions.ts`:
    - [x] Server Action for manual coordinate resolution
    - [x] Batch coordinate resolution for existing RFQs
    - [x] Coordinate validation and correction actions
  - [x] `distance.actions.ts`:
    - [x] Server Action for manual distance recalculation
    - [x] Batch distance calculation using cached coordinates
    - [x] Distance validation and correction actions
  - [x] **RFQ-specific geocoding actions consolidated into `rfq.actions.ts`**:
    - [x] Server Action for updating RFQ coordinates
    - [x] Server Action for updating RFQ distance
    - [x] Server Action for batch RFQ geocoding updates
    - [x] Server Action for checking RFQ geocoding status

### Phase 3: Route Visualization Components
- [x] **3.1** Create `route-visualization.service.ts` in `src/lib/services/google-maps/`
  - [x] Real-time route rendering using Google Routes API with cached coordinates
  - [x] Route polyline generation and styling
  - [x] Marker positioning and customization
  - [x] Map bounds calculation for optimal viewing

- [x] **3.2** Create `route-map.tsx` component in `src/app/dashboard/rfqs/[id]/_components/`
  - [x] Interactive Google Maps implementation using @vis.gl/react-google-maps
  - [x] Display cached distance and coordinates from RFQ data
  - [x] Route polyline rendering via Routes API using cached coordinates
  - [x] Origin/destination markers with custom icons at cached coordinate positions
  - [x] **Integrated Loading States**: Inline skeleton UI with conditional rendering
    - [x] Map container skeleton placeholder during initial load
    - [x] Distance information skeleton while data loads
    - [x] Progressive loading: distance first, then map visualization
    - [x] Consistent with existing Steelflow skeleton patterns
  - [x] **Comprehensive Error States**: Built-in error handling and fallbacks
    - [x] Graceful handling of missing coordinates
    - [x] API failure recovery with user-friendly messages
    - [x] Fallback to address-based rendering when coordinates unavailable
  - [x] **State Management**: Single component handling all UI states
    - [x] Loading, loaded, error, and empty states
    - [x] Smooth transitions between states
    - [x] Proper cleanup and memory management

### Phase 4: Tab Integration
- [x] **4.1** Update `rfq-detail-content.tsx`
  - [x] Add "Route Map" tab to existing tab structure
  - [x] Update TabsList to include 3 tabs (grid-cols-3)
  - [x] Add Route icon from lucide-react
  - [x] Integrate RouteMap component in TabsContent
  - [x] Pass cached distance data to component

- [x] **4.2** Update `rfq-detail-skeleton.tsx`
  - [x] Add third tab skeleton for "Route Map" tab
  - [x] **Note**: Route map content skeleton is handled internally by `route-map.tsx` component

### Phase 5: Data Migration & Backfill
- [x] **5.1** Create coordinate resolution migration script
  - [x] **Step 1**: Batch resolve coordinates for existing RFQ addresses
  - [x] **Step 2**: Store resolved coordinates in new database columns
  - [x] **Step 3**: Generate address hashes for change detection
  - [x] Handle geocoding failures and retry mechanisms
  - [x] Implement progress tracking and error recovery
  - [x] Validate coordinate accuracy against known locations

- [x] **5.2** Create distance calculation migration script
  - [x] **Step 1**: Batch calculate distances using resolved coordinates
  - [x] **Step 2**: Store calculated distances and metadata
  - [x] **Step 3**: Validate distance calculations against manual checks
  - [x] Handle calculation failures and retry mechanisms
  - [x] Implement progress tracking and completion verification

- [x] **5.3** Data validation and quality assurance
  - [x] Verify coordinate-address consistency
  - [x] Validate distance calculations against expected ranges
  - [x] Identify and handle edge cases (islands, ferry routes, etc.)
  - [x] Create data quality reports and monitoring

### Phase 6: Testing & Polish
- [x] **6.1** Responsive design implementation
  - [x] Mobile-friendly map sizing (h-64 on mobile, h-96 on tablet/desktop, h-[500px] on large screens)
  - [x] Tablet layout optimization (responsive spacing and layout adjustments)
  - [x] Desktop full-width utilization (larger map height and optimized spacing)
  - [x] **User-Requested Enhancement**: Moved Estimated Duration and Route Distance from Route Map card to Route Information card
  - [x] **Route Map Simplification**: Route Map card now contains only the interactive map visualization

- [x] **6.2** Performance optimization
  - [x] Lazy loading of map components (Suspense-based lazy loading with consolidated architecture)
  - [x] Efficient re-rendering strategies (React.memo, useMemo, useCallback optimizations)
  - [x] Distance cache validation and refresh mechanisms (cache staleness detection and status indicators)
  - [x] **Architectural Consolidation**: Merged lazy-route-map.tsx into route-map.tsx for better maintainability

## Acceptance Criteria

### Functional Requirements
- [x] **Route Visualization**: Map displays accurate route between origin and destination locations
- [x] **Cached Distance Display**: Shows pre-calculated route distance instantly from database
- [x] **Interactive Map**: Users can pan, zoom, and interact with the map
- [x] **Marker Identification**: Clear visual distinction between origin and destination markers
- [x] **Tab Integration**: Seamlessly integrated as third tab alongside "Providers" and "Email History"
- [x] **Distance Accuracy**: Cached distances match real-time calculations within 5% variance

### Technical Requirements
- [x] **Error Handling**: Graceful handling of missing coordinates, API failures, and network issues
- [x] **Integrated Loading States**: Single component with inline skeleton UI and progressive loading
- [x] **Responsive Design**: Works correctly on mobile, tablet, and desktop devices
- [x] **Performance**: Distance displays immediately, map loads within 3 seconds
- [x] **Accessibility**: Meets WCAG 2.1 AA standards
- [x] **API Efficiency**: 90% reduction in real-time distance API calls
- [x] **Component Architecture**: Consolidated route map component with internal state management

### Data Requirements
- [x] **Coordinate Caching**: All RFQs have resolved coordinates stored in database (origin_lat, origin_lng, destination_lat, destination_lng)
- [x] **Distance Caching**: All RFQs have calculated distances stored in database
- [x] **Flexible Address Processing**: System processes RFQs with partial address data (city + postal code minimum)
- [x] **Address-Coordinate Relationship**: Clear mapping between address fields and resolved coordinates using flexible building strategy
- [x] **Geocoding Fallback**: Falls back to geocoding when coordinates are missing, uses flexible address building
- [x] **Data Validation**: Validates coordinate data before distance calculation
- [x] **Cache Invalidation**: Recalculates coordinates and distances when addresses are modified
- [x] **Change Detection**: Address hash system detects when re-geocoding is needed (includes country information)
- [x] **Migration Completeness**: All existing RFQs with minimum address data have backfilled coordinate and distance data
- [x] **Data Consistency**: Coordinates and distances remain synchronized with address changes
- [x] **Partial Address Support**: System successfully processes RFQs with only city + postal code data
- [x] **Country-Enhanced Geocoding**: System includes country information in address building for improved accuracy

### UI/UX Requirements
- [x] **Visual Consistency**: Matches existing Steelflow design patterns and color scheme
- [x] **Tab Behavior**: Consistent tab switching behavior with existing tabs
- [x] **Integrated Loading Experience**: Single component with smooth transitions and inline skeletons
- [x] **Progressive Loading**: Distance information loads first, followed by map visualization
- [x] **Error States**: User-friendly error messages with actionable guidance built into component
- [x] **Mobile Experience**: Optimized touch interactions and readable text on mobile devices
- [x] **State Transitions**: Seamless transitions between loading, loaded, error, and empty states

## Risk Mitigation

### Technical Risks
- **Google Maps API Limits**: Implement distance caching to reduce API usage by 90%
- **Distance Calculation Failures**: Graceful fallbacks and retry mechanisms for failed calculations
- **Database Performance**: Index distance columns and optimize queries for large datasets
- **Coordinate Accuracy**: Validate coordinates and provide geocoding fallbacks
- **Cache Staleness**: Implement change detection and selective recalculation

### User Experience Risks
- **Missing Location Data**: Provide clear messaging and alternative information display
- **Instant Loading**: Cached distances eliminate loading delays for distance information
- **Mobile Usability**: Ensure touch-friendly interactions and readable content
- **Data Inconsistency**: Monitor and validate cached distances against real-time calculations

### Integration Risks
- **Database Migration**: Careful rollout of schema changes with zero-downtime deployment
- **Existing Code Impact**: Minimize changes to existing RFQ components
- **Service Layer Compliance**: Ensure proper separation of concerns
- **Error Propagation**: Prevent route map errors from affecting other tabs
- **API Cost Management**: Monitor and alert on unexpected API usage spikes

## Success Metrics

### User Engagement
- **Tab Usage**: >30% of RFQ detail page views include Route Map tab interaction
- **Session Duration**: Increased time spent on RFQ detail pages
- **User Feedback**: Positive feedback on route visualization utility

### Technical Performance
- **Distance Load Time**: Cached distances display instantly (0ms) for 100% of requests
- **Map Load Time**: Route visualization loads within 3 seconds for 95% of requests
- **API Usage Reduction**: 90% reduction in Distance Matrix API calls compared to real-time approach
- **Cache Hit Rate**: >95% of distance requests served from cache
- **Error Rate**: <2% error rate for distance calculations and route visualizations
- **Database Performance**: Distance queries execute within 50ms

### Business Impact
- **Cost Efficiency**: Significant reduction in Google Maps API costs through caching
- **Decision Making**: Improved logistics planning efficiency with instant distance access
- **Provider Selection**: Enhanced context for provider evaluation
- **User Satisfaction**: Increased platform utility and user retention
- **Data Quality**: Consistent and reliable distance information across all RFQs
